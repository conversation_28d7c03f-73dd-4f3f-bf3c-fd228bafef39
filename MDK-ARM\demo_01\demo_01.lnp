--cpu=Cortex-M4.fp.sp
"demo_01\startup_stm32f429xx.o"
"demo_01\main.o"
"demo_01\gpio.o"
"demo_01\tim.o"
"demo_01\stm32f4xx_it.o"
"demo_01\stm32f4xx_hal_msp.o"
"demo_01\stm32f4xx_hal_tim.o"
"demo_01\stm32f4xx_hal_tim_ex.o"
"demo_01\stm32f4xx_hal_rcc.o"
"demo_01\stm32f4xx_hal_rcc_ex.o"
"demo_01\stm32f4xx_hal_flash.o"
"demo_01\stm32f4xx_hal_flash_ex.o"
"demo_01\stm32f4xx_hal_flash_ramfunc.o"
"demo_01\stm32f4xx_hal_gpio.o"
"demo_01\stm32f4xx_hal_dma_ex.o"
"demo_01\stm32f4xx_hal_dma.o"
"demo_01\stm32f4xx_hal_pwr.o"
"demo_01\stm32f4xx_hal_pwr_ex.o"
"demo_01\stm32f4xx_hal_cortex.o"
"demo_01\stm32f4xx_hal.o"
"demo_01\stm32f4xx_hal_exti.o"
"demo_01\system_stm32f4xx.o"
"demo_01\scheduler.o"
"demo_01\led_app.o"
--library_type=microlib --strict --scatter "demo_01\demo_01.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "demo_01.map" -o demo_01\demo_01.axf